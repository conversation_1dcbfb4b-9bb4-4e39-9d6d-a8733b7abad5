"""
Production-ready document extraction service using AWS Textract and Bedrock Nova Pro.

This module provides a comprehensive document extraction pipeline that:
1. Uses AWS Textract for document analysis and OCR
2. Uses Bedrock Nova Pro model for intelligent data extraction
3. Processes documents directly from S3 using S3 keys
4. Includes comprehensive logging for AWS environments
5. Handles async operations for production scalability
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

import logging
import asyncio
import traceback
from datetime import datetime
from typing import Dict, Any, Optional
from app.core.configuration import settings
from app.utils.textract import process_document_with_textract
from app.llm.bedrock import extract_data_with_bedrock, extract_data_with_structured_text_bedrock

# Configure logging for AWS environments
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()  # This will output to terminal/CloudWatch
    ]
)
logger = logging.getLogger(__name__)


class DocumentExtractionProcessor:
    """
    Production-ready document extraction processor using AWS Textract and Bedrock Nova Pro.

    This class handles the complete extraction pipeline:
    - Document analysis using AWS Textract
    - Intelligent data extraction using Bedrock Nova Pro
    - Error handling and retry logic
    - Comprehensive logging for production monitoring
    """

    def __init__(self, s3_client, aws_account_id: str, aws_region: Optional[str] = None,
                 aws_access_key_id: Optional[str] = None, aws_secret_access_key: Optional[str] = None):
        """
        Initialize the extraction processor.

        Args:
            s3_client: Pre-configured S3 client
            aws_account_id: AWS account ID
            aws_region: AWS region (defaults to settings)
            aws_access_key_id: AWS access key (defaults to settings)
            aws_secret_access_key: AWS secret key (defaults to settings)
        """
        logger.info("🚀 Initializing DocumentExtractionProcessor...")

        try:
            self.s3_client = s3_client
            self.aws_account_id = aws_account_id
            self.region = aws_region or settings.AWS_REGION
            self.aws_access_key_id = aws_access_key_id or settings.AWS_ACCESS_KEY_ID
            self.aws_secret_access_key = aws_secret_access_key or settings.AWS_SECRET_ACCESS_KEY

            logger.info("✅ Successfully initialized DocumentExtractionProcessor")
            logger.info(f"   AWS Account ID: {self.aws_account_id}")
            logger.info(f"   AWS Region: {self.region}")
            logger.info(f"   Timestamp: {datetime.now().isoformat()}")

        except Exception as e:
            logger.error(f"❌ Failed to initialize DocumentExtractionProcessor: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Timestamp: {datetime.now().isoformat()}")
            logger.error(f"   Traceback: {traceback.format_exc()}")
            raise
    
    def extract_s3_info(self, s3_key: str, bucket_name: Optional[str] = None) -> tuple:
        """
        Extract bucket and key information from S3 key or URI.

        Args:
            s3_key: S3 key or full S3 URI
            bucket_name: Optional bucket name if s3_key is just the key

        Returns:
            tuple: (bucket_name, object_key)
        """
        logger.debug(f"📍 Extracting S3 info from: {s3_key}")

        if s3_key.startswith('s3://'):
            # Full S3 URI provided
            parts = s3_key[5:].split('/', 1)
            bucket = parts[0]
            key = parts[1] if len(parts) > 1 else ''
            logger.debug(f"   Extracted from URI - Bucket: {bucket}, Key: {key}")
            return bucket, key
        else:
            # Just the key provided, use provided bucket_name or default
            bucket = bucket_name or settings.S3_BUCKET_NAME
            if not bucket:
                raise ValueError("Bucket name must be provided either in s3_key URI or bucket_name parameter")
            logger.debug(f"   Using provided key - Bucket: {bucket}, Key: {s3_key}")
            return bucket, s3_key
    
    async def analyze_document_with_textract(self, s3_uri: str) -> Dict[str, Any]:
        """
        Analyze document using AWS Textract for OCR and document analysis.

        Args:
            s3_uri: S3 URI in format s3://bucket-name/object-key

        Returns:
            dict: Textract analysis results with both plain text and structured text
        """
        return await process_document_with_textract(
            s3_uri,
            self.region, self.aws_access_key_id, self.aws_secret_access_key
        )
    
    async def extract_data_with_bedrock_nova(self, text_content: str,
                                           system_prompt: Optional[str] = None,
                                           user_prompt: Optional[str] = None,
                                           temperature: float = 0.1,
                                           max_tokens: int = 4000,
                                           top_p: float = 0.9) -> Dict[str, Any]:
        """
        Extract structured data using Bedrock Nova Pro model.

        Args:
            text_content: Text content to analyze
            system_prompt: Optional custom system prompt
            user_prompt: Optional custom user prompt
            temperature: Model temperature
            max_tokens: Maximum tokens
            top_p: Top-p sampling

        Returns:
            dict: Extracted structured data
        """
        return await extract_data_with_bedrock(
            text_content, system_prompt, user_prompt, temperature, max_tokens, top_p,
            self.region, self.aws_access_key_id, self.aws_secret_access_key
        )

    async def extract_data_with_structured_text_bedrock_nova(self, plain_text: str, structured_text: str,
                                                           system_prompt: Optional[str] = None,
                                                           user_prompt: Optional[str] = None,
                                                           temperature: float = 0.1,
                                                           max_tokens: int = 4000,
                                                           top_p: float = 0.9) -> Dict[str, Any]:
        """
        Extract structured data using Bedrock Nova Pro model with both plain text and structured text with coordinates.

        Args:
            plain_text: Plain text content
            structured_text: Structured text with coordinates
            system_prompt: Optional custom system prompt
            user_prompt: Optional custom user prompt
            temperature: Model temperature
            max_tokens: Maximum tokens
            top_p: Top-p sampling

        Returns:
            dict: Extracted structured data using coordinate-aware processing
        """
        return await extract_data_with_structured_text_bedrock(
            plain_text, structured_text, system_prompt, user_prompt, temperature, max_tokens, top_p,
            self.region, self.aws_access_key_id, self.aws_secret_access_key
        )



    async def process_document_extraction(self, s3_key: str, bucket_name: Optional[str] = None,
                                        extraction_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        Complete document extraction pipeline: Textract + Bedrock Nova Pro.

        Args:
            s3_key: S3 key or full S3 URI of the document
            bucket_name: Optional bucket name if s3_key is just the key
            extraction_prompt: Optional custom extraction prompt for Bedrock

        Returns:
            dict: Complete extraction results including metadata
        """
        logger.info("🎯 Starting complete document extraction pipeline...")
        logger.info(f"   S3 Key: {s3_key}")
        logger.info(f"   Bucket: {bucket_name}")
        logger.info(f"   Timestamp: {datetime.now().isoformat()}")
        logger.info("="*80)

        start_time = datetime.now()

        try:
            # Extract S3 information
            bucket, object_key = self.extract_s3_info(s3_key, bucket_name)

            # Step 1: Analyze document with Textract
            logger.info("📋 Step 1: Document analysis with Textract")
            s3_uri = f"s3://{bucket}/{object_key}"
            textract_result = await self.analyze_document_with_textract(s3_uri)

            # Step 2: Extract text from Textract result
            logger.info("📋 Step 2: Text extraction from Textract results")
            extracted_text = textract_result.get('extracted_text', '')
            structured_text = textract_result.get('structured_text', '')

            # Step 3: Extract structured data with Bedrock Nova Pro using both plain and structured text
            logger.info("📋 Step 3: Enhanced structured data extraction with Bedrock Nova Pro")
            logger.info("   Using both plain text and coordinate-aware structured text")
            bedrock_result = await self.extract_data_with_structured_text_bedrock_nova(
                extracted_text, structured_text, user_prompt=extraction_prompt
            )

            # Compile final results
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            final_result = {
                "extraction_metadata": {
                    "s3_location": f"s3://{bucket}/{object_key}",
                    "processing_start_time": start_time.isoformat(),
                    "processing_end_time": end_time.isoformat(),
                    "total_processing_time_seconds": processing_time,
                    "textract_processing_method": textract_result.get('textract_metadata', {}).get('processing_method', 'unknown'),
                    "extracted_text_length": len(extracted_text),
                    "structured_text_length": len(structured_text),
                    "aws_region": self.region,
                    "aws_account_id": self.aws_account_id
                },
                "textract_metadata": textract_result.get('textract_metadata', {}),
                "textract_raw_result": textract_result.get('textract_raw_result', {}),
                "extracted_text": extracted_text,
                "structured_text": structured_text,
                "structured_data": bedrock_result.get('structured_data', {})
            }

            logger.info("🎉 Document extraction pipeline completed successfully!")
            logger.info(f"   Total processing time: {processing_time:.2f} seconds")
            logger.info(f"   Final result keys: {list(final_result.keys())}")
            logger.info(f"   Completion timestamp: {end_time.isoformat()}")
            logger.info("="*80)

            return final_result

        except Exception as e:
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            logger.error("❌ Document extraction pipeline failed!")
            logger.error(f"   Error: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Processing time before failure: {processing_time:.2f} seconds")
            logger.error(f"   Failure timestamp: {end_time.isoformat()}")
            logger.error(f"   Traceback: {traceback.format_exc()}")
            logger.error("="*80)
            raise


# Utility functions for production use
async def extract_document_data(s3_key: str, s3_client, aws_account_id: str,
                              bucket_name: Optional[str] = None,
                              extraction_prompt: Optional[str] = None,
                              aws_region: Optional[str] = None,
                              aws_access_key_id: Optional[str] = None,
                              aws_secret_access_key: Optional[str] = None) -> Dict[str, Any]:
    """
    Main entry point for document extraction.

    This is the primary function to be called for document extraction in production.

    Args:
        s3_key: S3 key or full S3 URI of the document to process
        s3_client: Pre-configured S3 client
        aws_account_id: AWS account ID
        bucket_name: Optional bucket name if s3_key is just the key
        extraction_prompt: Optional custom prompt for data extraction
        aws_region: AWS region (defaults to settings)
        aws_access_key_id: AWS access key (defaults to settings)
        aws_secret_access_key: AWS secret key (defaults to settings)

    Returns:
        dict: Complete extraction results

    Example:
        # Using S3 URI
        result = await extract_document_data('s3://my-bucket/documents/invoice.pdf', s3_client, account_id)

        # Using S3 key with bucket name
        result = await extract_document_data('documents/invoice.pdf', s3_client, account_id, 'my-bucket')
    """
    logger.info("🚀 Starting document extraction process...")

    try:
        processor = DocumentExtractionProcessor(
            s3_client, aws_account_id, aws_region, aws_access_key_id, aws_secret_access_key
        )
        result = await processor.process_document_extraction(s3_key, bucket_name, extraction_prompt)

        logger.info("✅ Document extraction completed successfully")
        return result

    except Exception as e:
        logger.error(f"❌ Document extraction failed: {str(e)}")
        logger.error(f"   Traceback: {traceback.format_exc()}")
        raise


def extract_key_fields(extraction_result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract key business fields from the extraction result for easy access.
    Returns main extraction of key-value pairs along with metadata.

    Args:
        extraction_result: Result from extract_document_data function

    Returns:
        dict: Main extraction with key business fields and metadata
    """
    logger.info("📊 Extracting key business fields...")

    try:
        structured_data = extraction_result.get('structured_data', {})

        # Initialize the main extraction result following the reference pattern
        main_extraction = {}

        # Extract key fields directly from structured_data (following reference file pattern)
        if isinstance(structured_data, dict):
            # Extract vendor name
            vendor_name = structured_data.get('vendor_name', '')
            main_extraction['vendor_name'] = vendor_name if vendor_name else ''

            # Extract invoice number
            invoice_number = structured_data.get('invoice_number', '')
            main_extraction['invoice_number'] = invoice_number if invoice_number else ''

            # Extract invoice date
            invoice_date = structured_data.get('invoice_date', '')
            main_extraction['invoice_date'] = invoice_date if invoice_date else ''

            # Extract invoice amount (handle both string and numeric values)
            invoice_amount = structured_data.get('invoice_amount', '')
            if invoice_amount and str(invoice_amount).strip():
                try:
                    # Try to convert to float if it's a string
                    if isinstance(invoice_amount, str):
                        # Remove currency symbols and convert to float
                        amount_str = invoice_amount.replace('$', '').replace(',', '').strip()
                        main_extraction['invoice_amount'] = float(amount_str)
                    else:
                        main_extraction['invoice_amount'] = float(invoice_amount)
                except (ValueError, TypeError):
                    main_extraction['invoice_amount'] = invoice_amount
            else:
                main_extraction['invoice_amount'] = ''

            # Extract remit_to information (following reference file structure)
            remit_to = structured_data.get('remit_to', {})
            if remit_to and isinstance(remit_to, dict):
                main_extraction['remit_to'] = {
                    'remit_to_name': remit_to.get('remit_to_name', ''),
                    'remit_to_address1': remit_to.get('remit_to_address1', ''),
                    'remit_to_address_2': remit_to.get('remit_to_address_2', ''),
                    'remit_to_city': remit_to.get('remit_to_city', ''),
                    'remit_to_state_province': remit_to.get('remit_to_state_province', ''),
                    'remit_to_postal_code': remit_to.get('remit_to_postal_code', ''),
                    'remit_to_country': remit_to.get('remit_to_country', '')
                }
            else:
                main_extraction['remit_to'] = None

        # Add extraction metadata (following reference file pattern)
        extraction_metadata = extraction_result.get('extraction_metadata', {})
        textract_metadata = extraction_result.get('textract_metadata', {})

        main_extraction['extraction_metadata'] = {
            'source_document': extraction_metadata.get('s3_location', 'unknown'),
            'extraction_timestamp': datetime.now().isoformat(),
            'processing_method': textract_metadata.get('processing_method', 'unknown'),
            'total_processing_time_seconds': extraction_metadata.get('total_processing_time_seconds', 0),
            'extracted_text_length': extraction_metadata.get('extracted_text_length', 0),
            'structured_text_length': extraction_metadata.get('structured_text_length', 0),
            'aws_region': extraction_metadata.get('aws_region', 'unknown')
        }

        logger.info("✅ Key fields extraction completed")
        logger.info(f"   Vendor: {main_extraction.get('vendor_name', 'N/A')}")
        logger.info(f"   Invoice Number: {main_extraction.get('invoice_number', 'N/A')}")
        logger.info(f"   Invoice Date: {main_extraction.get('invoice_date', 'N/A')}")
        logger.info(f"   Invoice Amount: {main_extraction.get('invoice_amount', 'N/A')}")

        return main_extraction

    except Exception as e:
        logger.error(f"❌ Error extracting key fields: {str(e)}")
        logger.error(f"   Error type: {type(e).__name__}")
        logger.error(f"   Traceback: {traceback.format_exc()}")
        raise


# Main function for testing and standalone execution
async def main(s3_key: str = 's3://document-extraction-logistically/temp/11173426_carrier_invoice.pdf',
               bucket_name: Optional[str] = None):
    """
    Main function for testing the extraction pipeline.

    Args:
        s3_key: S3 key or URI of the document to process
        bucket_name: Optional bucket name
    """
    import boto3

    logger.info("🎯 Starting Document Extraction Pipeline")
    logger.info(f"   Input S3 Key: {s3_key}")
    logger.info(f"   Bucket Name: {bucket_name}")
    logger.info(f"   Timestamp: {datetime.now().isoformat()}")
    logger.info("="*80)

    try:
        # Create required clients
        s3_client = boto3.client(
            's3',
            region_name=settings.AWS_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
        )

        sts_client = boto3.client(
            'sts',
            region_name=settings.AWS_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
        )

        aws_account_id = sts_client.get_caller_identity().get('Account')

        # Run the extraction
        TEMPERACTURE = 0
        TOP_P = 0.9
        MAX_TOKENS = 4000
        result = await extract_document_data(s3_key, s3_client, aws_account_id, bucket_name)

        # Extract key fields for summary
        key_fields = extract_key_fields(result)

        # Log summary
        logger.info("\n📊 EXTRACTION SUMMARY:")
        logger.info(f"   Vendor: {result['structured_data']['data']}")

        return result

    except Exception as e:
        logger.error(f"\n❌ Pipeline failed with error: {str(e)}")
        logger.error(f"   Error type: {type(e).__name__}")
        logger.error(f"   Timestamp: {datetime.now().isoformat()}")
        logger.error(f"   Traceback: {traceback.format_exc()}")
        raise


if __name__ == "__main__":
    # Example usage
    import sys

    # Get S3 key from command line argument or use default
    s3_key = sys.argv[1] if len(sys.argv) > 1 else 's3://document-extraction-logistically/temp/11173426_carrier_invoice.pdf'

    # Run the extraction pipeline
    asyncio.run(main(s3_key))
