"""
AWS Bedrock utility functions for AI-powered data extraction.

This module provides production-ready Bedrock functionality for:
- Nova Pro model integration
- Structured data extraction from text
- Custom prompt handling
- Comprehensive logging for AWS environments
"""

import boto3
import json
import logging
import traceback
from datetime import datetime
from typing import Dict, Any, Optional
from botocore.exceptions import ClientError
from app.core.configuration import settings
from app.utils.text_utils import extract_json_from_backticks

# Configure logging
logger = logging.getLogger(__name__)


class BedrockProcessor:
    """
    AWS Bedrock processor for AI-powered data extraction using Nova Pro.
    """
    
    def __init__(self, aws_region: str, aws_access_key_id: str, aws_secret_access_key: str):
        """
        Initialize Bedrock processor.
        
        Args:
            aws_region: AWS region
            aws_access_key_id: AWS access key ID
            aws_secret_access_key: AWS secret access key
        """
        logger.info("🚀 Initializing BedrockProcessor...")
        
        try:
            self.bedrock_client = boto3.client(
                'bedrock-runtime',
                region_name=aws_region,
                aws_access_key_id=aws_access_key_id,
                aws_secret_access_key=aws_secret_access_key
            )
            
            self.region = aws_region
            
            logger.info("✅ Successfully initialized BedrockProcessor")
            logger.info(f"   AWS Region: {self.region}")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize BedrockProcessor: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Traceback: {traceback.format_exc()}")
            raise
    
    def get_default_system_prompt(self) -> str:
        """
        Get the default system prompt for document extraction.

        Returns:
            str: Default system prompt
        """
        return """You are an expert document data extraction AI assistant. Your task is to analyze document text and extract key business information in a structured JSON format.

You should extract the following information when available:
- Vendor/Company Name
- Invoice Number/Document ID
- Date (Invoice Date, Document Date)
- Total Amount
- Currency
- Customer Information
- Line Items (if applicable)
- Any other relevant business data

Always return valid JSON format with clear field names. If a field is not found, use null as the value.
Be precise and accurate in your extraction."""

    def get_structured_text_system_prompt(self) -> str:
        """
        Get the system prompt for structured text with coordinates extraction.

        Returns:
            str: System prompt for structured text processing
        """
        return """You are an expert document data extraction AI assistant specialized in processing structured text with coordinate information.

The input text contains both plain text and structured text with bounding box coordinates in the format:
text, x1, y1, x2, y2

Where:
- text: The actual text content
- x1, y1: Top-left corner coordinates (normalized 0.0-1.0)
- x2, y2: Bottom-right corner coordinates (normalized 0.0-1.0)

Your task is to:
1. Analyze both the plain text content and the coordinate information
2. Extract key business information in structured JSON format
3. Use coordinate information to understand document layout and relationships
4. Identify fields that are spatially related (e.g., labels and values)

Extract the following information when available:
- Vendor/Company Name
- Invoice Number/Document ID
- Date (Invoice Date, Document Date)
- Total Amount
- Currency
- Customer Information
- Line Items (if applicable)
- Any other relevant business data

Always return valid JSON format with clear field names. If a field is not found, use null as the value.
Be precise and accurate in your extraction, leveraging both text content and spatial relationships."""
    
    def get_default_user_prompt(self, text_content: str) -> str:
        """
        Get the default user prompt for document extraction.
        
        Args:
            text_content: Document text to analyze
            
        Returns:
            str: Formatted user prompt
        """
        return f"""Please analyze the following document text and extract key business information in JSON format:

Document Text:
{text_content}

Extract the information and return as a valid JSON object."""
    
    async def extract_data_with_nova_pro(self, text_content: str, 
                                       system_prompt: Optional[str] = None,
                                       user_prompt: Optional[str] = None,
                                       temperature: float = 0.1,
                                       max_tokens: int = 4000,
                                       top_p: float = 0.9) -> Dict[str, Any]:
        """
        Extract structured data using Bedrock Nova Pro model.
        
        Args:
            text_content: Text content to analyze
            system_prompt: Optional custom system prompt
            user_prompt: Optional custom user prompt
            temperature: Model temperature (default: 0.1)
            max_tokens: Maximum tokens (default: 4000)
            top_p: Top-p sampling (default: 0.9)
            
        Returns:
            dict: Extracted structured data
        """
        logger.info("🧠 Starting data extraction...")
        logger.info(f"   Text length: {len(text_content)} characters")
        logger.info(f"   Temperature: {temperature}")
        logger.info(f"   Max tokens: {max_tokens}")
        
        try:
            # Use provided prompts or defaults
            if not system_prompt:
                system_prompt = self.get_default_system_prompt()

            if not user_prompt:
                user_prompt = self.get_default_user_prompt(text_content)
            
            # Call Bedrock Nova Pro using converse method
            response = self.bedrock_client.converse(
                modelId="amazon.nova-pro-v1:0",
                system=[{"text": system_prompt}],
                messages=[
                    {
                        "role": "user",
                        "content": [{"text": user_prompt}]
                    }
                ],
                inferenceConfig={
                    'maxTokens': max_tokens,
                    'temperature': temperature,
                    'topP': top_p,
                }
            )

            logger.info("✅ Bedrock Nova Pro extraction completed successfully!")

            # Extract the content from Nova Pro response using converse format
            if 'output' in response and 'message' in response['output']:
                content = response['output']['message']['content']
                if len(content) > 0 and 'text' in content[0]:
                    extracted_content = content[0]['text']
                    logger.info(f"   Extracted content length: {len(extracted_content)} characters")

                    # Try to parse as JSON
                    try:
                        extracted_data = extract_json_from_backticks(extracted_content)
                        logger.info(f"   Extracted fields: {list(extracted_data.keys()) if isinstance(extracted_data, dict) else 'Non-dict response'}")
                        return extracted_data
                    except json.JSONDecodeError:
                        logger.warning("⚠️  Extracted content is not valid JSON, returning as text")
                        return {"extracted_text": extracted_content, "raw_response": response}
                else:
                    logger.warning("⚠️  Unexpected content structure in  response")
                    return {"raw_response": response}
            else:
                logger.warning("⚠️  Unexpected response format")
                return {"raw_response": response}
                
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            logger.error(f"❌ Bedrock ClientError: {error_code} - {error_message}")
            logger.error(f"   Traceback: {traceback.format_exc()}")
            raise

        except Exception as e:
            logger.error(f"❌ Unexpected error during Bedrock extraction: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Traceback: {traceback.format_exc()}")
            raise
    
    async def process_text_extraction(self, text_content: str,
                                    system_prompt: Optional[str] = None,
                                    user_prompt: Optional[str] = None,
                                    temperature: float = 0.1,
                                    max_tokens: int = 4000,
                                    top_p: float = 0.9) -> Dict[str, Any]:
        """
        Complete Bedrock processing: extract structured data from text.
        
        Args:
            text_content: Text content to analyze
            system_prompt: Optional custom system prompt
            user_prompt: Optional custom user prompt
            temperature: Model temperature
            max_tokens: Maximum tokens
            top_p: Top-p sampling
            
        Returns:
            dict: Complete Bedrock processing results
        """
        logger.info("🎯 Starting complete Bedrock processing...")
        logger.info(f"   Text length: {len(text_content)} characters")
        
        start_time = datetime.now()
        
        try:
            # Extract structured data
            structured_data = await self.extract_data_with_nova_pro(
                text_content, system_prompt, user_prompt, temperature, max_tokens, top_p
            )
            
                

            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            result = {
                "bedrock_metadata": {
                    "processing_start_time": start_time.isoformat(),
                    "processing_end_time": end_time.isoformat(),
                    "total_processing_time_seconds": processing_time,
                    "input_text_length": len(text_content),
                    "model_id": "amazon.nova-pro-v1:0",
                    "temperature": temperature,
                    "max_tokens": max_tokens,
                    "top_p": top_p,
                    "aws_region": self.region
                },
                "structured_data": structured_data
            }
            
            logger.info("✅ Bedrock processing completed successfully!")
            logger.info(f"   Total processing time: {processing_time:.2f} seconds")
            
            return result
            
        except Exception as e:
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            logger.error("❌ Bedrock processing failed!")
            logger.error(f"   Error: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Processing time before failure: {processing_time:.2f} seconds")
            logger.error(f"   Failure timestamp: {end_time.isoformat()}")
            logger.error(f"   Traceback: {traceback.format_exc()}")
            raise

    async def extract_data_with_structured_text(self, plain_text: str, structured_text: str,
                                              system_prompt: Optional[str] = None,
                                              user_prompt: Optional[str] = None,
                                              temperature: float = 0,
                                              max_tokens: int = 8000,
                                              top_p: float = 1) -> Dict[str, Any]:
        """
        Extract structured data using both plain text and structured text with coordinates.

        Args:
            plain_text: Plain text content
            structured_text: Structured text with coordinates
            system_prompt: Optional custom system prompt
            user_prompt: Optional custom user prompt
            temperature: Model temperature
            max_tokens: Maximum tokens
            top_p: Top-p sampling

        Returns:
            dict: Complete Bedrock processing results
        """
        logger.info("🎯 Starting structured text processing with coordinates...")
        logger.info(f"   Plain text length: {len(plain_text)} characters")
        logger.info(f"   Structured text length: {len(structured_text)} characters")

        start_time = datetime.now()

        try:
            # Use structured text system prompt if not provided
            if not system_prompt:
                system_prompt = """
                ## Task Summary:
                    You are a completely obedient accountant who is an expert at structured data extraction from US based invoices. Follow steps mentioned in "Model Instructions".

                ## Model Instructions:
                    Step 1: The conversion of a PDF to a text invoice is provided in UserContent in the following csv like structure 
                    === TEXT WITH COORDINATES ===
                    text, x1, y1, x2, y2
                    [actual_text], [x1], [y1], [x2], [y2]

                    Here x1, y1, x2, y2 represents standard bounding box coordinates of actual text.  

                    Step 2: Consider bounding box coordinates and reconstruct the exact layout for proper information fetching by considering the relative location of the text perfectly. 

                    Step 3: Find relevant information from the invoice and fill out the required output JSON file. If something is not found in the invoice then keep the respective value of the output as ''. 

                    Step 4: Check again all extraction pairs and correct it, if required, so that all information is accurately extracted from all pages in the output JSON. 
                    
                    Step 5: Strickly refer and follow comments mentioned in "Response Schema" json structure and make sure that all information is extracted correctly. DO NOT ASSUME ANYTHING.

                ## Response Schema:
                    ```json
                    {
                        // The name of the vendor issuing the invoice (e.g., company or individual name).
                        "vendor_name": "[actual_vendor_name]",

                        // The unique identifier like invoice number or reference number for the invoice.
                        "invoice_number": "[actual_invoice_number]",

                        // The date when the invoice was issued, strickly formatted as "YYYY-MM-DD" (e.g., "2023-12-31").
                        // Give only if date is explicitly mentioned as invoice date or Bill date.
                        // In invoice date might be in MM/DD/YYYY or MM-DD-YYYY or similar format. Convert it to YYYY-MM-DD format.
                        "invoice_date": "[actual_date_in_YYYY-MM-DD_format]",

                        // The total amount due on the invoice, strickly in numeric format (e.g., 1234.56).
                        // Must be a number (float or integer). Exclude currency symbols. Use null if not found or ambiguous.
                        // Invoice total must be present and you dont have to calculate it.
                        // If multiple totals exist (e.g., subtotal and tax), use the final total amount due.
                        "invoice_amount": [actual_amount_in_float],

                        // Extremely important to include remit to details only and only if it is explicitly mentioned as "Remit to" in the document otherwise keep it empty.
                        "remit_to": {

                            // The name of the entity to which payment should be sent (Mentioned as remit to in invoice).
                            "remit_to_name": "[actual_remit_to_name]",

                            // The primary street address line for payment remittance (e.g., "123 Main St").
                            "remit_to_address1": "[actual_remit_to_address1]",

                            // The secondary street address line for payment remittance (e.g., "Suite 100"). Optional, use null if not applicable.
                            "remit_to_address_2": "[actual_remit_to_address_2]",

                            // The city for payment remittance (e.g., "Springfield").
                            "remit_to_city": "[actual_remit_to_city]",

                            // The state or province for payment remittance (e.g., "IL" or "Ontario").
                            "remit_to_state_province": "[actual_remit_to_state_province]",

                            // The postal or ZIP code for payment remittance (e.g., "62701").
                            "remit_to_postal_code": "[actual_remit_to_postal_code]",

                            // The country for payment remittance (e.g., "USA" or "Canada").
                            "remit_to_country": "[actual_remit_to_country]"
                        } or 
                        // If remit to details are not explicitly mentioned as "Remit to" in the document then keep it empty.
                        "null"
                    }```
            """

            # Create enhanced user prompt with both text types
            if not user_prompt:
                user_prompt = ""

            # Extract structured data using the enhanced prompt
            structured_data = await self.extract_data_with_nova_pro(
                "",
                system_prompt, user_prompt, temperature, max_tokens, top_p
            )

            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            result = {
                "bedrock_metadata": {
                    "processing_start_time": start_time.isoformat(),
                    "processing_end_time": end_time.isoformat(),
                    "total_processing_time_seconds": processing_time,
                    "input_plain_text_length": len(plain_text),
                    "input_structured_text_length": len(structured_text),
                    "model_id": "amazon.nova-pro-v1:0",
                    "temperature": temperature,
                    "max_tokens": max_tokens,
                    "top_p": top_p,
                    "processing_mode": "structured_text_with_coordinates",
                    "aws_region": self.region
                },
                "structured_data": structured_data
            }

            logger.info("✅ Structured text processing completed successfully!")
            logger.info(f"   Total processing time: {processing_time:.2f} seconds")

            return result

        except Exception as e:
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            logger.error("❌ Structured text processing failed!")
            logger.error(f"   Error: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Processing time before failure: {processing_time:.2f} seconds")
            logger.error(f"   Failure timestamp: {end_time.isoformat()}")
            logger.error(f"   Traceback: {traceback.format_exc()}")
            raise


# Utility functions for easy access
async def extract_data_with_bedrock(text_content: str,
                                  system_prompt: Optional[str] = None,
                                  user_prompt: Optional[str] = None,
                                  temperature: float = 0.1,
                                  max_tokens: int = 4000,
                                  top_p: float = 0.9,
                                  aws_region: Optional[str] = None,
                                  aws_access_key_id: Optional[str] = None,
                                  aws_secret_access_key: Optional[str] = None) -> Dict[str, Any]:
    """
    Extract structured data using Bedrock Nova Pro.
    
    Args:
        text_content: Text content to analyze
        system_prompt: Optional custom system prompt
        user_prompt: Optional custom user prompt
        temperature: Model temperature
        max_tokens: Maximum tokens
        top_p: Top-p sampling
        aws_region: AWS region (defaults to settings)
        aws_access_key_id: AWS access key (defaults to settings)
        aws_secret_access_key: AWS secret key (defaults to settings)
        
    Returns:
        dict: Bedrock processing results
    """
    # Use provided credentials or fall back to settings
    region = aws_region or settings.AWS_REGION
    access_key = aws_access_key_id or settings.AWS_ACCESS_KEY_ID
    secret_key = aws_secret_access_key or settings.AWS_SECRET_ACCESS_KEY
    
    processor = BedrockProcessor(region, access_key, secret_key)
    return await processor.process_text_extraction(
        text_content, system_prompt, user_prompt, temperature, max_tokens, top_p
    )


async def extract_data_with_structured_text_bedrock(plain_text: str, structured_text: str,
                                                  system_prompt: Optional[str] = None,
                                                  user_prompt: Optional[str] = None,
                                                  temperature: float = 0.1,
                                                  max_tokens: int = 4000,
                                                  top_p: float = 0.9,
                                                  aws_region: Optional[str] = None,
                                                  aws_access_key_id: Optional[str] = None,
                                                  aws_secret_access_key: Optional[str] = None) -> Dict[str, Any]:
    """
    Extract structured data using both plain text and structured text with coordinates.

    Args:
        plain_text: Plain text content
        structured_text: Structured text with coordinates
        system_prompt: Optional custom system prompt
        user_prompt: Optional custom user prompt
        temperature: Model temperature
        max_tokens: Maximum tokens
        top_p: Top-p sampling
        aws_region: AWS region (defaults to settings)
        aws_access_key_id: AWS access key (defaults to settings)
        aws_secret_access_key: AWS secret key (defaults to settings)

    Returns:
        dict: Bedrock processing results with enhanced coordinate-aware extraction
    """
    # Use provided credentials or fall back to settings
    region = aws_region or settings.AWS_REGION
    access_key = aws_access_key_id or settings.AWS_ACCESS_KEY_ID
    secret_key = aws_secret_access_key or settings.AWS_SECRET_ACCESS_KEY

    processor = BedrockProcessor(region, access_key, secret_key)
    return await processor.extract_data_with_structured_text(
        plain_text, structured_text, system_prompt, user_prompt, temperature, max_tokens, top_p
    )
