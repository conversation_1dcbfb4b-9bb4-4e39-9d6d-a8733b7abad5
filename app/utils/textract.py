"""
AWS Textract utility functions for document analysis and OCR.

This module provides production-ready Textract functionality for:
- Document analysis with OCR
- Text extraction from various document types
- Async operations for scalability
- Comprehensive logging for AWS environments
"""
import sys
import os
# Add the repository root to Python path so 'app' module can be imported
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

import boto3
import asyncio
import logging
import traceback
import io
from datetime import datetime
from typing import Dict, Any, Optional, Tuple
from botocore.exceptions import ClientError
from pathlib import Path
from urllib.parse import urlparse
from app.core.configuration import settings

try:
    import PyPDF2
    PDF_SUPPORT = True
except ImportError:
    PDF_SUPPORT = False

# Configure logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
if not logger.hasHandlers():
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s %(levelname)s %(name)s: %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)


def parse_s3_uri(s3_uri: str) -> Tuple[str, str]:
    """
    Parse S3 URI into bucket name and object key.

    Args:
        s3_uri: S3 URI in format s3://bucket-name/object-key

    Returns:
        tuple: (bucket_name, object_key)

    Raises:
        ValueError: If URI format is invalid
    """
    try:
        parsed = urlparse(s3_uri)
        if parsed.scheme != 's3':
            raise ValueError(f"Invalid S3 URI scheme. Expected 's3', got '{parsed.scheme}'")

        bucket_name = parsed.netloc
        object_key = parsed.path.lstrip('/')

        if not bucket_name:
            raise ValueError("Bucket name is missing from S3 URI")
        if not object_key:
            raise ValueError("Object key is missing from S3 URI")

        return bucket_name, object_key

    except Exception as e:
        raise ValueError(f"Failed to parse S3 URI '{s3_uri}': {str(e)}")


class TextractProcessor:
    """
    AWS Textract processor for document analysis and text extraction.
    """
    
    def __init__(self, aws_region: str, aws_access_key_id: str, aws_secret_access_key: str):
        """
        Initialize Textract processor.

        Args:
            aws_region: AWS region
            aws_access_key_id: AWS access key ID
            aws_secret_access_key: AWS secret access key
        """
        logger.info("🚀 Initializing TextractProcessor...")

        try:
            self.textract_client = boto3.client(
                'textract',
                region_name=aws_region,
                aws_access_key_id=aws_access_key_id,
                aws_secret_access_key=aws_secret_access_key
            )

            self.s3_client = boto3.client(
                's3',
                region_name=aws_region,
                aws_access_key_id=aws_access_key_id,
                aws_secret_access_key=aws_secret_access_key
            )

            self.region = aws_region

            # Supported file formats for sync operations
            self.supported_formats = {'.pdf', '.jpg', '.jpeg', '.png', '.tiff', '.tif'}
            self.max_file_size = 10 * 1024 * 1024  # 10 MB limit for sync operations

            logger.info("✅ Successfully initialized TextractProcessor")
            logger.info(f"   AWS Region: {self.region}")
            logger.info(f"   Supported sync formats: {self.supported_formats}")

        except Exception as e:
            logger.error(f"❌ Failed to initialize TextractProcessor: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Traceback: {traceback.format_exc()}")
            raise

    def _get_file_metadata(self, bucket_name: str, object_key: str) -> Dict[str, Any]:
        """
        Get file metadata from S3.

        Args:
            bucket_name: S3 bucket name
            object_key: S3 object key

        Returns:
            dict: File metadata including size and content type
        """
        try:
            response = self.s3_client.head_object(Bucket=bucket_name, Key=object_key)
            return {
                'size': response['ContentLength'],
                'content_type': response.get('ContentType', ''),
                'last_modified': response['LastModified']
            }
        except ClientError as e:
            logger.error(f"❌ Failed to get file metadata: {str(e)}")
            logger.error(f"   Traceback: {traceback.format_exc()}")
            raise

    def _count_pdf_pages(self, bucket_name: str, object_key: str) -> int:
        """
        Count pages in a PDF file from S3.

        Args:
            bucket_name: S3 bucket name
            object_key: S3 object key

        Returns:
            int: Number of pages in PDF, or 1 if unable to determine
        """
        if not PDF_SUPPORT:
            logger.warning("⚠️  PyPDF2 not available, assuming 1 page for PDF")
            return 1

        try:
            # Download PDF content
            response = self.s3_client.get_object(Bucket=bucket_name, Key=object_key)
            pdf_content = response['Body'].read()

            # Create PDF reader
            pdf_stream = io.BytesIO(pdf_content)
            pdf_reader = PyPDF2.PdfReader(pdf_stream)

            page_count = len(pdf_reader.pages)
            return page_count

        except Exception as e:
            logger.warning(f"⚠️  Failed to count PDF pages: {str(e)}")
            logger.warning("   Assuming 1 page for sync processing")
            return 1

    def _should_use_sync_method(self, bucket_name: str, object_key: str) -> Tuple[bool, str]:
        """
        Determine if sync method should be used based on file criteria.

        Criteria for sync method:
        - File extension is supported (.pdf, .jpg, .jpeg, .png, .tiff, .tif)
        - File size is under 10 MB limit
        - For PDF: single page only (calculated, not assumed)
        - For TIFF: single page assumed

        Args:
            bucket_name: S3 bucket name
            object_key: S3 object key

        Returns:
            tuple: (can_use_sync, reason)
        """
        s3_location = f"s3://{bucket_name}/{object_key}"

        try:
            # Get file metadata
            metadata = self._get_file_metadata(bucket_name, object_key)
            file_size = metadata['size']
            file_size_mb = file_size / (1024 * 1024)

            # Check file extension
            file_ext = Path(object_key).suffix.lower()
            if file_ext not in self.supported_formats:
                reason = f"Unsupported format: {file_ext}. Supported: {self.supported_formats}"
                logger.info(f"   Decision: ASYNC - {reason}")
                return False, reason

            # Check file size
            if file_size > self.max_file_size:
                max_size_mb = self.max_file_size / (1024 * 1024)
                reason = f"File size ({file_size_mb:.2f} MB) exceeds {max_size_mb:.0f} MB limit"
                logger.info(f"   Size: {file_size_mb:.2f} MB")
                logger.info(f"   Decision: ASYNC - {reason}")
                return False, reason

            # For PDF, count actual pages
            if file_ext == '.pdf':
                page_count = self._count_pdf_pages(bucket_name, object_key)
                if page_count > 1:
                    reason = f"PDF has {page_count} pages (sync supports single page only)"
                    logger.info(f"   Size: {file_size_mb:.2f} MB")
                    logger.info(f"   Format: {file_ext} (supported)")
                    logger.info(f"   Decision: ASYNC - {reason}")
                    return False, reason
                logger.info(f"   PDF is single page, suitable for sync processing")

            # For TIFF, assume single page (could be enhanced later)
            elif file_ext in ['.tiff', '.tif']:
                logger.info(f"   TIFF detected. Assuming single page for sync processing.")

            reason = f"File suitable for synchronous processing"
            logger.info(f"   Size: {file_size_mb:.2f} MB")
            logger.info(f"   Format: {file_ext} (supported)")
            logger.info(f"   Decision: SYNC - {reason}")

            return True, reason

        except Exception as e:
            reason = f"Failed to analyze file for sync/async decision: {str(e)}"
            logger.warning(f"⚠️  {reason}")
            logger.warning(f"   Traceback: {traceback.format_exc()}")
            logger.warning("   Defaulting to ASYNC method")
            return False, reason

    def analyze_document_sync(self, bucket_name: str, object_key: str) -> Dict[str, Any]:
        """
        Analyze document using synchronous Textract detect_document_text.

        Args:
            bucket_name: S3 bucket name
            object_key: S3 object key

        Returns:
            dict: Textract analysis results
        """
        logger.info(f"📄 Starting SYNC Textract document analysis...")

        try:
            # Use detect_document_text for sync processing
            response = self.textract_client.detect_document_text(
                Document={
                    'S3Object': {
                        'Bucket': bucket_name,
                        'Name': object_key
                    }
                }
            )

            return response

        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            logger.error(f"❌ SYNC Textract ClientError: {error_code} - {error_message}")
            logger.error(f"   Traceback: {traceback.format_exc()}")
            raise

        except Exception as e:
            logger.error(f"❌ Unexpected error during SYNC Textract analysis: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Traceback: {traceback.format_exc()}")
            raise

    async def analyze_document(self, bucket_name: str, object_key: str) -> Dict[str, Any]:
        """
        Analyze document using AWS Textract for OCR and document analysis.
        
        Args:
            bucket_name: S3 bucket name
            object_key: S3 object key
            
        Returns:
            dict: Textract analysis results
        """
        logger.info(f"📄 Starting ASYNC Textract document analysis...")
        
        try:
            # Start document analysis
            response = self.textract_client.start_document_analysis(
                DocumentLocation={
                    'S3Object': {
                        'Bucket': bucket_name,
                        'Name': object_key
                    }
                },
                FeatureTypes=['TABLES', 'FORMS', 'LAYOUT']
            )
            
            job_id = response['JobId']
            logger.info(f"✅ Textract analysis started successfully")
            logger.info(f"   Job ID: {job_id}")
            
            # Poll for completion
            logger.info("⏳ Waiting for Textract analysis to complete...")
            poll_count = 0
            start_time = datetime.now()
            
            while True:
                poll_count += 1
                elapsed_time = (datetime.now() - start_time).total_seconds()
                
                result = self.textract_client.get_document_analysis(JobId=job_id)
                status = result['JobStatus']
                
                logger.info(f"   Poll #{poll_count} - Status: {status} (Elapsed: {elapsed_time:.1f}s)")
                
                if status == 'SUCCEEDED':
                    logger.info(f"✅ ASYNC Textract analysis completed successfully!")
                    logger.info(f"   Total processing time: {elapsed_time:.1f} seconds")
                    logger.info(f"   Total polls: {poll_count}")
                    return result
                    
                elif status == 'FAILED':
                    error_msg = f"Textract analysis failed after {elapsed_time:.1f} seconds"
                    logger.error(f"❌ {error_msg}")
                    logger.error(f"   Job ID: {job_id}")
                    raise Exception(error_msg)
                    
                elif status in ['IN_PROGRESS']:
                    logger.debug(f"   ⏳ Analysis still in progress... waiting 5 seconds")
                    await asyncio.sleep(5)
                else:
                    logger.warning(f"   ⚠️  Unknown status: {status}")
                    await asyncio.sleep(5)
                    
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            logger.error(f"❌ ASYNC Textract ClientError: {error_code} - {error_message}")
            logger.error(f"   Traceback: {traceback.format_exc()}")
            raise

        except Exception as e:
            logger.error(f"❌ Unexpected error during ASYNC Textract analysis: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Traceback: {traceback.format_exc()}")
            raise

    def _convert_textract_to_structured_format(self, textract_response: Dict[str, Any]) -> str:
        """
        Convert Textract response to structured text format with coordinates.

        Args:
            textract_response: Textract API response

        Returns:
            str: Structured text with coordinates in CSV format
        """
        try:
            blocks = textract_response.get('Blocks', [])

            # Extract text blocks with coordinates in the required format
            text_lines = []

            # Process LINE blocks for text with coordinates (most efficient)
            for block in blocks:
                if block['BlockType'] == 'LINE':
                    bbox = block.get('Geometry', {}).get('BoundingBox', {})
                    text = block.get('Text', '').replace(',', ';')  # Replace commas to avoid CSV issues

                    # Convert to x1, y1, x2, y2 format
                    x1 = bbox.get('Left', 0)
                    y1 = bbox.get('Top', 0)
                    x2 = x1 + bbox.get('Width', 0)
                    y2 = y1 + bbox.get('Height', 0)

                    text_lines.append(f"{text}, {x1:.4f}, {y1:.4f}, {x2:.4f}, {y2:.4f}")

            # If no LINE blocks found (sync response), use WORD blocks
            if not text_lines:
                logger.info("   No LINE blocks found, using WORD blocks for text extraction")
                for block in blocks:
                    if block['BlockType'] == 'WORD':
                        bbox = block.get('Geometry', {}).get('BoundingBox', {})
                        text = block.get('Text', '').replace(',', ';')  # Replace commas to avoid CSV issues

                        # Convert to x1, y1, x2, y2 format
                        x1 = bbox.get('Left', 0)
                        y1 = bbox.get('Top', 0)
                        x2 = x1 + bbox.get('Width', 0)
                        y2 = y1 + bbox.get('Height', 0)

                        text_lines.append(f"{text}, {x1:.4f}, {y1:.4f}, {x2:.4f}, {y2:.4f}")

            # Build the structured text format (text only, no tables for speed)
            structured_text_parts = []

            # Add header
            structured_text_parts.append("=== TEXT WITH COORDINATES ===")
            structured_text_parts.append("text, x1, y1, x2, y2")

            # Add text lines
            for line in text_lines:
                structured_text_parts.append(line)

            structured_text = "\n".join(structured_text_parts)

            logger.info(f"   Extracted {len(text_lines)} text lines with coordinates")
            return structured_text

        except Exception as e:
            logger.error(f"❌ Error converting Textract response to structured format: {str(e)}")
            logger.error(f"   Traceback: {traceback.format_exc()}")
            raise

    def extract_text_from_result(self, textract_result: Dict[str, Any]) -> Dict[str, str]:
        """
        Extract clean text and structured text with coordinates from Textract analysis result.

        Args:
            textract_result: Result from Textract analysis

        Returns:
            dict: Contains 'plain_text' and 'structured_text' with coordinates
        """

        try:
            blocks = textract_result.get('Blocks', [])

            # Extract plain text (existing functionality with WORD fallback)
            text_blocks = [block for block in blocks if block['BlockType'] == 'LINE']
            if text_blocks:
                plain_text = '\n'.join([block.get('Text', '') for block in text_blocks])
            else:
                # Fallback to WORD blocks if no LINE blocks found (sync response)
                word_blocks = [block for block in blocks if block['BlockType'] == 'WORD']
                plain_text = ' '.join([block.get('Text', '') for block in word_blocks])

            # Extract structured text with coordinates
            structured_text = self._convert_textract_to_structured_format(textract_result)

            logger.info(f"   Characters detected: {len(plain_text)}")

            return {
                'plain_text': plain_text,
                'structured_text': structured_text
            }

        except Exception as e:
            logger.error(f"❌ Error extracting text from Textract result: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Traceback: {traceback.format_exc()}")
            raise
    
    async def process_document_from_s3_uri(self, s3_uri: str) -> Dict[str, Any]:
        """
        Complete Textract processing using S3 URI: tries sync first, then async if needed.

        Args:
            s3_uri: S3 URI in format s3://bucket-name/object-key

        Returns:
            dict: Complete Textract processing results
        """
        bucket_name, object_key = parse_s3_uri(s3_uri)
        return await self.process_document(bucket_name, object_key)

    async def process_document(self, bucket_name: str, object_key: str) -> Dict[str, Any]:
        """
        Complete Textract processing: tries sync first, then async if needed.

        Args:
            bucket_name: S3 bucket name
            object_key: S3 object key

        Returns:
            dict: Complete Textract processing results
        """
        s3_location = f"s3://{bucket_name}/{object_key}"
        logger.info(f"🎯 Starting Textract processing: {s3_location}")

        start_time = datetime.now()
        processing_method = "UNKNOWN"

        try:
            # Determine processing method based on file criteria
            should_use_sync, reason = self._should_use_sync_method(bucket_name, object_key)

            if should_use_sync:
                processing_method = "SYNC"
                try:
                    # Try sync method first
                    textract_result = self.analyze_document_sync(bucket_name, object_key)

                except Exception as sync_error:
                    logger.warning(f"⚠️  SYNC processing failed: {str(sync_error)}")
                    logger.warning(f"   Sync failure reason: {reason}")
                    logger.warning(f"   Traceback: {traceback.format_exc()}")
                    logger.info("🔄 Falling back to ASYNC processing...")
                    processing_method = "ASYNC_FALLBACK"
                    textract_result = await self.analyze_document(bucket_name, object_key)
                    logger.info("✅ ASYNC fallback processing successful!")
            else:
                logger.info(f"🚀 Using ASYNC processing directly... Reason: {reason}")
                processing_method = "ASYNC"
                textract_result = await self.analyze_document(bucket_name, object_key)
                logger.info("✅ ASYNC processing successful!")

            # Extract text (both plain and structured)
            text_results = self.extract_text_from_result(textract_result)
            plain_text = text_results['plain_text']
            structured_text = text_results['structured_text']

            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            result = {
                "textract_metadata": {
                    "s3_location": s3_location,
                    "processing_start_time": start_time.isoformat(),
                    "processing_end_time": end_time.isoformat(),
                    "total_processing_time_seconds": processing_time,
                    "processing_method": processing_method,
                    "extracted_text_length": len(plain_text),
                    "aws_region": self.region
                },
                "textract_raw_result": textract_result,
                "extracted_text": plain_text,
                "structured_text": structured_text
            }

            logger.info("✅ Textract processing completed successfully!")
            logger.info(f"   Total processing time: {processing_time:.2f} seconds")

            return result

        except Exception as e:
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            logger.error("❌ Textract processing failed!")
            logger.error(f"   Processing method attempted: {processing_method}")
            logger.error(f"   Error: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Processing time before failure: {processing_time:.2f} seconds")
            logger.error(f"   Failure timestamp: {end_time.isoformat()}")
            logger.error(f"   Traceback: {traceback.format_exc()}")
            raise



async def process_document_with_textract(s3_uri: str,
                                         aws_region: Optional[str] = None,
                                         aws_access_key_id: Optional[str] = None,
                                         aws_secret_access_key: Optional[str] = None) -> Dict[str, Any]:
    """
    Process document using intelligent Textract (sync first, then async fallback).

    This function automatically determines the best processing method:
    - For supported formats (PDF, JPG, PNG, TIFF) under 10MB: tries sync first, falls back to async
    - For other files or larger files: uses async directly

    Args:
        s3_uri: S3 URI in format s3://bucket-name/object-key
        aws_region: AWS region (defaults to settings)
        aws_access_key_id: AWS access key (defaults to settings)
        aws_secret_access_key: AWS secret key (defaults to settings)

    Returns:
        dict: Textract processing results with processing method metadata
    """
    # Parse S3 URI
    bucket_name, object_key = parse_s3_uri(s3_uri)

    # Use provided credentials or fall back to settings
    region = aws_region or settings.AWS_REGION
    access_key = aws_access_key_id or settings.AWS_ACCESS_KEY_ID
    secret_key = aws_secret_access_key or settings.AWS_SECRET_ACCESS_KEY

    processor = TextractProcessor(region, access_key, secret_key)
    return await processor.process_document(bucket_name, object_key)


# Sample usage
async def main():
    # Example usage - replace with actual S3 URI
    # The file must already be uploaded to S3 before calling Textract
    result = await process_document_with_textract(
        's3://document-extraction-logistically/temp/11173426_carrier_invoice.pdf'  # S3 URI
    )
    print("Processing completed successfully!")
    print(f"Extracted text length: {len(result['extracted_text'])}")
    print(f"Structured text available: {'structured_text' in result}")
    print(f"Processing method: {result['textract_metadata']['processing_method']}")
    print(f"Processing time: {result['textract_metadata']['total_processing_time_seconds']:.2f} seconds")

    # Show sample of structured text
    if 'structured_text' in result:
        structured_lines = result['structured_text'].split('\n')
        print(f"Structured text preview (first 5 lines):")
        for line in structured_lines[:5]:
            print(f"  {line}")
        if len(structured_lines) > 5:
            print(f"  ... and {len(structured_lines) - 5} more lines")


if __name__ == "__main__":
    asyncio.run(main())