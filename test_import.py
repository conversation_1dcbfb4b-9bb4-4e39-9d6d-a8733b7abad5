#!/usr/bin/env python3
"""
Test script to verify that the textract module can be imported correctly.
"""

try:
    from app.utils.textract import TextractProcessor
    print("✅ Import successful! The textract module can be imported correctly.")
    print("✅ TextractProcessor class is available.")
except ImportError as e:
    print(f"❌ Import failed: {e}")
except Exception as e:
    print(f"❌ Unexpected error: {e}")
